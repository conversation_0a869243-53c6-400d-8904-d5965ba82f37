<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Student;
use App\Models\StudentEnrollment;
use App\Models\Course;
use App\Models\ClassEnrollment;
use App\Models\ShsStudent;
use App\Jobs\ExportStudentDataJob;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Collection;

final class StudentReportingService
{
    private GeneralSettingsService $settingsService;

    public function __construct(GeneralSettingsService $settingsService)
    {
        $this->settingsService = $settingsService;
    }

    public function generateDashboardReport(): array
    {
        // Get current academic period from settings
        $currentSchoolYear = $this->settingsService->getCurrentSchoolYearString();
        $currentSemester = $this->settingsService->getCurrentSemester();

        return [
            'overview' => $this->generateOverviewStats($currentSchoolYear, $currentSemester),
            'courses' => $this->generateCourseBreakdown($currentSchoolYear, $currentSemester),
            'year_levels' => $this->generateYearLevelBreakdown($currentSchoolYear, $currentSemester),
            'demographics' => $this->generateDemographics($currentSchoolYear, $currentSemester),
            'academic_period' => [
                'school_year' => $currentSchoolYear,
                'semester' => $currentSemester,
                'semester_label' => $this->settingsService->getAvailableSemesters()[$currentSemester] ?? 'Unknown',
            ],
            'generated_at' => now()->format('Y-m-d H:i:s'),
        ];
    }

    private function generateOverviewStats(string $schoolYear, int $semester): array
    {
        // Get enrolled student IDs for current period
        $enrolledStudentIds = StudentEnrollment::where('school_year', $schoolYear)
            ->where('semester', $semester)
            ->whereNull('deleted_at')
            ->pluck('student_id')
            ->toArray();

        // Total students
        $totalStudents = Student::whereIn('id', $enrolledStudentIds)
            ->whereNull('deleted_at')
            ->count();

        // Gender distribution
        $genderStats = Student::whereIn('id', $enrolledStudentIds)
            ->whereNull('deleted_at')
            ->selectRaw('gender, COUNT(*) as count')
            ->groupBy('gender')
            ->pluck('count', 'gender')
            ->toArray();

        // Enrollment status
        $enrollmentStats = StudentEnrollment::where('school_year', $schoolYear)
            ->where('semester', $semester)
            ->whereNull('deleted_at')
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        return [
            'total_students' => $totalStudents,
            'total_shs_students' => ShsStudent::count(),
            'gender_distribution' => [
                'male' => $genderStats['Male'] ?? 0,
                'female' => $genderStats['Female'] ?? 0,
                'total' => array_sum($genderStats),
            ],
            'enrollment_status' => $enrollmentStats,
        ];
    }

    private function generateCourseBreakdown(string $schoolYear, int $semester): array
    {
        // Get enrolled student IDs for current period
        $enrolledStudentIds = StudentEnrollment::where('school_year', $schoolYear)
            ->where('semester', $semester)
            ->whereNull('deleted_at')
            ->pluck('student_id')
            ->toArray();

        // Get all courses with their enrolled students
        $courses = Course::with(['students' => function ($query) use ($enrolledStudentIds) {
                $query->whereIn('id', $enrolledStudentIds)->whereNull('deleted_at');
            }])
            ->orderBy('code')
            ->get()
            ->map(function ($course) use ($schoolYear, $semester) {
                $students = $course->students;

                // Year level breakdown
                $yearLevelBreakdown = $students->groupBy('academic_year')->map->count()->toArray();

                // Gender breakdown
                $genderBreakdown = $students->groupBy('gender')->map->count()->toArray();

                return [
                    'course_code' => $course->code,
                    'course_title' => $course->title,
                    'total_students' => $students->count(),
                    'year_levels' => $yearLevelBreakdown,
                    'gender_distribution' => [
                        'male' => $genderBreakdown['Male'] ?? 0,
                        'female' => $genderBreakdown['Female'] ?? 0,
                    ],
                    'average_age' => $students->avg('age') ?? 0,
                ];
            })
            ->filter(fn($course) => $course['total_students'] > 0); // Only show courses with students

        return $courses->toArray();
    }

    private function generateYearLevelBreakdown(string $schoolYear, int $semester): array
    {
        // Get enrolled student IDs for current period
        $enrolledStudentIds = StudentEnrollment::where('school_year', $schoolYear)
            ->where('semester', $semester)
            ->whereNull('deleted_at')
            ->pluck('student_id')
            ->toArray();

        $yearLevels = [1, 2, 3, 4];

        $yearLevelData = collect($yearLevels)->map(function ($year) use ($enrolledStudentIds) {
            $students = Student::where('academic_year', $year)
                ->whereIn('id', $enrolledStudentIds)
                ->whereNull('deleted_at')
                ->with('course')
                ->get();

            // Program breakdown
            $programBreakdown = $students->groupBy('course.code')->map->count()->toArray();

            // Gender breakdown
            $genderBreakdown = $students->groupBy('gender')->map->count()->toArray();

            return [
                'year_level' => $year,
                'year_label' => $this->getYearLevelLabel($year),
                'total_students' => $students->count(),
                'programs' => $programBreakdown,
                'gender_distribution' => [
                    'male' => $genderBreakdown['Male'] ?? 0,
                    'female' => $genderBreakdown['Female'] ?? 0,
                ],
                'average_age' => $students->avg('age') ?? 0,
            ];
        })->filter(fn($yearLevel) => $yearLevel['total_students'] > 0);

        return $yearLevelData->toArray();
    }

    private function generateDemographics(string $schoolYear, int $semester): array
    {
        // Get enrolled student IDs for current period
        $enrolledStudentIds = StudentEnrollment::where('school_year', $schoolYear)
            ->where('semester', $semester)
            ->whereNull('deleted_at')
            ->pluck('student_id')
            ->toArray();

        $students = Student::whereIn('id', $enrolledStudentIds)
            ->whereNull('deleted_at')
            ->get();

        // Age statistics
        $ages = $students->pluck('age')->filter();

        // Age groups
        $ageGroups = $students->groupBy(function ($student) {
            $age = $student->age;
            if ($age < 18) return 'Under 18';
            if ($age <= 20) return '18-20';
            if ($age <= 22) return '21-22';
            if ($age <= 25) return '23-25';
            return 'Over 25';
        })->map->count()->toArray();

        return [
            'age_statistics' => [
                'average' => $ages->avg() ?? 0,
                'median' => $ages->median() ?? 0,
                'min' => $ages->min() ?? 0,
                'max' => $ages->max() ?? 0,
            ],
            'age_groups' => $ageGroups,
        ];
    }

    private function getYearLevelLabel(int $year): string
    {
        return match ($year) {
            1 => '1st Year',
            2 => '2nd Year',
            3 => '3rd Year',
            4 => '4th Year',
            default => 'Unknown Year',
        };
    }

    public function exportToExcel(): string
    {
        $reportData = $this->generateDashboardReport();

        // Create CSV content
        $csvContent = "Student Analytics Report\n";
        $csvContent .= "Generated: " . $reportData['generated_at'] . "\n";
        $csvContent .= "Academic Period: " . $reportData['academic_period']['school_year'] . " - " . $reportData['academic_period']['semester_label'] . "\n\n";

        // Overview section
        $csvContent .= "OVERVIEW\n";
        $csvContent .= "Total Students," . $reportData['overview']['total_students'] . "\n";
        $csvContent .= "Total SHS Students," . $reportData['overview']['total_shs_students'] . "\n";
        $csvContent .= "Male Students," . $reportData['overview']['gender_distribution']['male'] . "\n";
        $csvContent .= "Female Students," . $reportData['overview']['gender_distribution']['female'] . "\n\n";

        // Courses section
        $csvContent .= "COURSES\n";
        $csvContent .= "Course Code,Course Title,Total Students,Male,Female,Average Age\n";
        foreach ($reportData['courses'] as $course) {
            $csvContent .= $course['course_code'] . "," . $course['course_title'] . "," . $course['total_students'] . "," . $course['gender_distribution']['male'] . "," . $course['gender_distribution']['female'] . "," . number_format($course['average_age'], 1) . "\n";
        }

        $fileName = 'student_analytics_' . date('Y-m-d_H-i-s') . '.csv';
        $filePath = 'exports/' . $fileName;

        Storage::put($filePath, $csvContent);

        return $filePath;
    }

    public function generateExportPreview(array $filters): array
    {
        $students = $this->getFilteredStudents($filters, 10); // Limit to 10 for preview

        return [
            'students' => $students->map(function ($student) {
                return [
                    'student_id' => $student->id,
                    'full_name' => trim($student->first_name . ' ' . ($student->middle_name ? $student->middle_name . ' ' : '') . $student->last_name),
                    'course_code' => $student->course->code ?? 'N/A',
                    'year_level' => $student->academic_year,
                ];
            })->toArray(),
            'total_count' => $this->getFilteredStudents($filters)->count(),
            'filters_applied' => $filters,
        ];
    }

    public function queueExport(array $filters, string $format, int $userId): int
    {
        // Create export job record in database
        $exportJob = \App\Models\ExportJob::create([
            'job_id' => uniqid('export_', true),
            'user_id' => $userId,
            'export_type' => 'student_data',
            'filters' => $filters,
            'format' => $format,
            'status' => 'pending',
        ]);

        // Dispatch job with export job ID
        ExportStudentDataJob::dispatch($exportJob->id);

        return $exportJob->id;
    }

    public function generateFilteredExportContent(array $filters, string $format): string
    {
        $students = $this->getFilteredStudents($filters);

        if ($format === 'pdf') {
            return $this->generatePdfExportContent($students, $filters);
        } else {
            return $this->generateCsvExportContent($students, $filters);
        }
    }

    public function generateFilteredExport(array $filters, string $format): string
    {
        $students = $this->getFilteredStudents($filters);

        if ($format === 'pdf') {
            return $this->generatePdfExport($students, $filters);
        } else {
            return $this->generateCsvExport($students, $filters);
        }
    }

    private function getFilteredStudents(array $filters, ?int $limit = null): Collection
    {
        $currentSchoolYear = $this->settingsService->getCurrentSchoolYearString();
        $currentSemester = $this->settingsService->getCurrentSemester();

        // Get enrolled student IDs for current period
        $enrolledStudentIds = StudentEnrollment::where('school_year', $currentSchoolYear)
            ->where('semester', $currentSemester)
            ->whereNull('deleted_at')
            ->pluck('student_id')
            ->toArray();

        $query = Student::whereIn('id', $enrolledStudentIds)
            ->whereNull('deleted_at')
            ->with('course');

        // Apply course filter
        if (isset($filters['course_filter']) && $filters['course_filter'] !== 'all') {
            $query->whereHas('course', function ($q) use ($filters) {
                $q->where('code', 'LIKE', $filters['course_filter'] . '%');
            });
        }

        // Apply year level filter
        if (isset($filters['year_level_filter']) && $filters['year_level_filter'] !== 'all') {
            $query->where('academic_year', $filters['year_level_filter']);
        }

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    private function generateCsvExport(Collection $students, array $filters): string
    {
        $csvContent = "Student Export Report\n";
        $csvContent .= "Generated: " . now()->format('Y-m-d H:i:s') . "\n";
        $csvContent .= "Academic Period: " . $this->settingsService->getCurrentSchoolYearString() . " - " . $this->settingsService->getAvailableSemesters()[$this->settingsService->getCurrentSemester()] . "\n";

        // Add filter information
        if ($filters['course_filter'] !== 'all') {
            $csvContent .= "Course Filter: " . $filters['course_filter'] . "\n";
        }
        if ($filters['year_level_filter'] !== 'all') {
            $csvContent .= "Year Level Filter: " . $filters['year_level_filter'] . "\n";
        }

        $csvContent .= "\nSTUDENT LIST\n";
        $csvContent .= "Student ID,Full Name,Course Code,Year Level\n";

        foreach ($students as $student) {
            $fullName = trim($student->first_name . ' ' . ($student->middle_name ? $student->middle_name . ' ' : '') . $student->last_name);
            $csvContent .= $student->id . ',"' . $fullName . '",' . ($student->course->code ?? 'N/A') . ',' . $student->academic_year . "\n";
        }

        // Add summary
        $csvContent .= "\nSUMMARY\n";
        $csvContent .= "Total Students," . $students->count() . "\n";

        // Course breakdown
        $courseBreakdown = $students->groupBy('course.code')->map->count();
        $csvContent .= "\nCOURSE BREAKDOWN\n";
        $csvContent .= "Course,Count\n";
        foreach ($courseBreakdown as $course => $count) {
            $csvContent .= ($course ?? 'N/A') . ',' . $count . "\n";
        }

        // Year level breakdown
        $yearBreakdown = $students->groupBy('academic_year')->map->count();
        $csvContent .= "\nYEAR LEVEL BREAKDOWN\n";
        $csvContent .= "Year Level,Count\n";
        foreach ($yearBreakdown as $year => $count) {
            $csvContent .= $year . ',' . $count . "\n";
        }

        $fileName = 'student_export_' . date('Y-m-d_H-i-s') . '.csv';
        $filePath = 'exports/' . $fileName;

        Storage::put($filePath, $csvContent);

        return $filePath;
    }

    private function generatePdfExport(Collection $students, array $filters): string
    {
        $htmlContent = "<!DOCTYPE html><html><head><title>Student Export Report</title>";
        $htmlContent .= "<style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .filters { background: #f5f5f5; padding: 15px; margin-bottom: 20px; border-radius: 5px; }
            table { border-collapse: collapse; width: 100%; margin: 20px 0; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .summary { margin-top: 30px; }
            .summary-table { width: 50%; }
        </style>";
        $htmlContent .= "</head><body>";

        $htmlContent .= "<div class='header'>";
        $htmlContent .= "<h1>Student Export Report</h1>";
        $htmlContent .= "<p>Generated: " . now()->format('Y-m-d H:i:s') . "</p>";
        $htmlContent .= "<p>Academic Period: " . $this->settingsService->getCurrentSchoolYearString() . " - " . $this->settingsService->getAvailableSemesters()[$this->settingsService->getCurrentSemester()] . "</p>";
        $htmlContent .= "</div>";

        // Filters applied
        if ($filters['course_filter'] !== 'all' || $filters['year_level_filter'] !== 'all') {
            $htmlContent .= "<div class='filters'>";
            $htmlContent .= "<h3>Filters Applied:</h3>";
            if ($filters['course_filter'] !== 'all') {
                $htmlContent .= "<p><strong>Course:</strong> " . $filters['course_filter'] . "</p>";
            }
            if ($filters['year_level_filter'] !== 'all') {
                $htmlContent .= "<p><strong>Year Level:</strong> " . $filters['year_level_filter'] . "</p>";
            }
            $htmlContent .= "</div>";
        }

        // Student list
        $htmlContent .= "<h2>Student List</h2>";
        $htmlContent .= "<table>";
        $htmlContent .= "<tr><th>Student ID</th><th>Full Name</th><th>Course Code</th><th>Year Level</th></tr>";

        foreach ($students as $student) {
            $fullName = trim($student->first_name . ' ' . ($student->middle_name ? $student->middle_name . ' ' : '') . $student->last_name);
            $htmlContent .= "<tr>";
            $htmlContent .= "<td>" . $student->id . "</td>";
            $htmlContent .= "<td>" . htmlspecialchars($fullName) . "</td>";
            $htmlContent .= "<td>" . ($student->course->code ?? 'N/A') . "</td>";
            $htmlContent .= "<td>" . $student->academic_year . "</td>";
            $htmlContent .= "</tr>";
        }

        $htmlContent .= "</table>";

        // Summary
        $htmlContent .= "<div class='summary'>";
        $htmlContent .= "<h2>Summary</h2>";
        $htmlContent .= "<p><strong>Total Students:</strong> " . $students->count() . "</p>";

        // Course breakdown
        $courseBreakdown = $students->groupBy('course.code')->map->count();
        if ($courseBreakdown->count() > 1) {
            $htmlContent .= "<h3>Course Breakdown</h3>";
            $htmlContent .= "<table class='summary-table'>";
            $htmlContent .= "<tr><th>Course</th><th>Count</th></tr>";
            foreach ($courseBreakdown as $course => $count) {
                $htmlContent .= "<tr><td>" . ($course ?? 'N/A') . "</td><td>" . $count . "</td></tr>";
            }
            $htmlContent .= "</table>";
        }

        // Year level breakdown
        $yearBreakdown = $students->groupBy('academic_year')->map->count();
        if ($yearBreakdown->count() > 1) {
            $htmlContent .= "<h3>Year Level Breakdown</h3>";
            $htmlContent .= "<table class='summary-table'>";
            $htmlContent .= "<tr><th>Year Level</th><th>Count</th></tr>";
            foreach ($yearBreakdown as $year => $count) {
                $htmlContent .= "<tr><td>" . $year . "</td><td>" . $count . "</td></tr>";
            }
            $htmlContent .= "</table>";
        }

        $htmlContent .= "</div>";
        $htmlContent .= "</body></html>";

        $fileName = 'student_export_' . date('Y-m-d_H-i-s') . '.html';
        $filePath = 'exports/' . $fileName;

        Storage::put($filePath, $htmlContent);

        return $filePath;
    }

    private function generateCsvExportContent(Collection $students, array $filters): string
    {
        $csvContent = "Student Export Report\n";
        $csvContent .= "Generated: " . now()->format('Y-m-d H:i:s') . "\n";
        $csvContent .= "Academic Period: " . $this->settingsService->getCurrentSchoolYearString() . " - " . $this->settingsService->getAvailableSemesters()[$this->settingsService->getCurrentSemester()] . "\n";

        // Add filter information
        if ($filters['course_filter'] !== 'all') {
            $csvContent .= "Course Filter: " . $filters['course_filter'] . "\n";
        }
        if ($filters['year_level_filter'] !== 'all') {
            $csvContent .= "Year Level Filter: " . $filters['year_level_filter'] . "\n";
        }

        $csvContent .= "\nSTUDENT LIST\n";
        $csvContent .= "Student ID,Full Name,Course Code,Year Level\n";

        foreach ($students as $student) {
            $fullName = trim($student->first_name . ' ' . ($student->middle_name ? $student->middle_name . ' ' : '') . $student->last_name);
            $csvContent .= $student->id . ',"' . $fullName . '",' . ($student->course->code ?? 'N/A') . ',' . $student->academic_year . "\n";
        }

        // Add summary
        $csvContent .= "\nSUMMARY\n";
        $csvContent .= "Total Students," . $students->count() . "\n";

        // Course breakdown
        $courseBreakdown = $students->groupBy('course.code')->map->count();
        $csvContent .= "\nCOURSE BREAKDOWN\n";
        $csvContent .= "Course,Count\n";
        foreach ($courseBreakdown as $course => $count) {
            $csvContent .= ($course ?? 'N/A') . ',' . $count . "\n";
        }

        // Year level breakdown
        $yearBreakdown = $students->groupBy('academic_year')->map->count();
        $csvContent .= "\nYEAR LEVEL BREAKDOWN\n";
        $csvContent .= "Year Level,Count\n";
        foreach ($yearBreakdown as $year => $count) {
            $csvContent .= $year . ',' . $count . "\n";
        }

        return $csvContent;
    }

    private function generatePdfExportContent(Collection $students, array $filters): string
    {
        $htmlContent = "<!DOCTYPE html><html><head><title>Student Export Report</title>";
        $htmlContent .= "<style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .filters { background: #f5f5f5; padding: 15px; margin-bottom: 20px; border-radius: 5px; }
            table { border-collapse: collapse; width: 100%; margin: 20px 0; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .summary { margin-top: 30px; }
            .summary-table { width: 50%; }
        </style>";
        $htmlContent .= "</head><body>";

        $htmlContent .= "<div class='header'>";
        $htmlContent .= "<h1>Student Export Report</h1>";
        $htmlContent .= "<p>Generated: " . now()->format('Y-m-d H:i:s') . "</p>";
        $htmlContent .= "<p>Academic Period: " . $this->settingsService->getCurrentSchoolYearString() . " - " . $this->settingsService->getAvailableSemesters()[$this->settingsService->getCurrentSemester()] . "</p>";
        $htmlContent .= "</div>";

        // Filters applied
        if ($filters['course_filter'] !== 'all' || $filters['year_level_filter'] !== 'all') {
            $htmlContent .= "<div class='filters'>";
            $htmlContent .= "<h3>Filters Applied:</h3>";
            if ($filters['course_filter'] !== 'all') {
                $htmlContent .= "<p><strong>Course:</strong> " . $filters['course_filter'] . "</p>";
            }
            if ($filters['year_level_filter'] !== 'all') {
                $htmlContent .= "<p><strong>Year Level:</strong> " . $filters['year_level_filter'] . "</p>";
            }
            $htmlContent .= "</div>";
        }

        // Student list
        $htmlContent .= "<h2>Student List</h2>";
        $htmlContent .= "<table>";
        $htmlContent .= "<tr><th>Student ID</th><th>Full Name</th><th>Course Code</th><th>Year Level</th></tr>";

        foreach ($students as $student) {
            $fullName = trim($student->first_name . ' ' . ($student->middle_name ? $student->middle_name . ' ' : '') . $student->last_name);
            $htmlContent .= "<tr>";
            $htmlContent .= "<td>" . $student->id . "</td>";
            $htmlContent .= "<td>" . htmlspecialchars($fullName) . "</td>";
            $htmlContent .= "<td>" . ($student->course->code ?? 'N/A') . "</td>";
            $htmlContent .= "<td>" . $student->academic_year . "</td>";
            $htmlContent .= "</tr>";
        }

        $htmlContent .= "</table>";

        // Summary
        $htmlContent .= "<div class='summary'>";
        $htmlContent .= "<h2>Summary</h2>";
        $htmlContent .= "<p><strong>Total Students:</strong> " . $students->count() . "</p>";

        // Course breakdown
        $courseBreakdown = $students->groupBy('course.code')->map->count();
        if ($courseBreakdown->count() > 1) {
            $htmlContent .= "<h3>Course Breakdown</h3>";
            $htmlContent .= "<table class='summary-table'>";
            $htmlContent .= "<tr><th>Course</th><th>Count</th></tr>";
            foreach ($courseBreakdown as $course => $count) {
                $htmlContent .= "<tr><td>" . ($course ?? 'N/A') . "</td><td>" . $count . "</td></tr>";
            }
            $htmlContent .= "</table>";
        }

        // Year level breakdown
        $yearBreakdown = $students->groupBy('academic_year')->map->count();
        if ($yearBreakdown->count() > 1) {
            $htmlContent .= "<h3>Year Level Breakdown</h3>";
            $htmlContent .= "<table class='summary-table'>";
            $htmlContent .= "<tr><th>Year Level</th><th>Count</th></tr>";
            foreach ($yearBreakdown as $year => $count) {
                $htmlContent .= "<tr><td>" . $year . "</td><td>" . $count . "</td></tr>";
            }
            $htmlContent .= "</table>";
        }

        $htmlContent .= "</div>";
        $htmlContent .= "</body></html>";

        return $htmlContent;
    }
}
