<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\ClassResource\Pages;
use App\Filament\Resources\ClassResource\RelationManagers;
use App\Models\Classes;
use App\Models\Course;
use App\Models\Faculty;
use App\Models\Room;
use App\Models\ShsStrand;
use App\Models\ShsTrack;
use App\Models\StrandSubject;
use App\Models\Subject;
use App\Rules\ScheduleOverlapRule;
use App\Services\GeneralSettingsService;
use Filament\Forms;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Section as InfolistSection;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

final class ClassResource extends Resource
{
    protected static ?string $model = Classes::class;

    protected static ?string $navigationIcon = 'heroicon-o-academic-cap';

    protected static int $globalSearchResultsLimit = 5;

    protected static ?string $recordTitleAttribute = 'subject_code';

    public static function getGloballySearchableAttributes(): array
    {
        return ['subject_code', 'subject.title', 'shsSubject.title'];
    }

    public static function getGlobalSearchResultDetails(
        Model $record
    ): array {
        return [
            'id' => $record->id,
            'Subject Code' => $record->subject_code,
            'Subject Title' => $record->subject_title, // Uses the accessor that handles both College and SHS
            'Section' => $record->section,
            'Type' => $record->isShs() ? 'SHS' : 'College',
        ];
    }

    public static function getGlobalSearchResultUrl(
        Model $record
    ): string {
        return self::getUrl('edit', ['record' => $record]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make()
                    ->schema([
                        Card::make()->schema([
                            Grid::make(2)->schema([
                                Select::make('classification')
                                    ->label('Class Type')
                                    ->placeholder('Choose class type...')
                                    ->options([
                                        'college' => 'College',
                                        'shs' => 'Senior High School (SHS)',
                                    ])
                                    ->required()
                                    ->default('college')
                                    ->live()
                                    ->helperText('Select whether this is a College or Senior High School class')
                                    ->afterStateUpdated(function (Forms\Set $set, $state): void {
                                        // Clear related fields when classification changes
                                        $set('subject_code', null);
                                        $set('course_codes', null);
                                        $set('shs_track_id', null);
                                        $set('shs_strand_id', null);
                                        $set('academic_year', null);
                                        $set('grade_level', null);
                                    })
                                    ->validationMessages([
                                        'required' => 'Please select a class type to continue.',
                                    ]),

                                // College Subject Selection
                                Select::make('subject_id')
                                    ->label('Subject')
                                    ->options(function (Forms\Get $get) {
                                        $selectedCourses = $get('course_codes');

                                        // If no courses selected, show all subjects from enrollment courses
                                        if (empty($selectedCourses)) {
                                            $settingsService = app(
                                                GeneralSettingsService::class
                                            );
                                            $enrollmentCourses = $settingsService->getGlobalSettingsModel()
                                                ->enrollment_courses;
                                            if (empty($enrollmentCourses)) {
                                                // Fallback: show all subjects if no enrollment courses configured
                                                return Subject::all()->mapWithKeys(fn ($subject) => [$subject->id => "{$subject->code} - {$subject->title}"]);
                                            }
                                            $courseIds = $enrollmentCourses;
                                        } else {
                                            $courseIds = $selectedCourses;
                                        }

                                        return Subject::with('course')
                                            ->whereIn('course_id', $courseIds)
                                            ->orderBy('code')
                                            ->get()
                                            ->mapWithKeys(function ($subject) {
                                                $courseCode = $subject->course
                                                    ? $subject->course->code
                                                    : 'No Course';
                                                // Show the exact subject code (including any trailing spaces) for clarity
                                                $display = "{$subject->code} - {$subject->title} ({$courseCode})";

                                                return [
                                                    $subject->id => $display,
                                                ];
                                            });
                                    })
                                    ->searchable()
                                    ->required()
                                    ->preload()
                                    ->live()
                                    ->visible(fn (Forms\Get $get): bool => $get('classification') === 'college')
                                    ->helperText('Choose the subject to be taught in this class.')
                                    ->createOptionForm([
                                        TextInput::make('code')
                                            ->label('Subject Code')
                                            ->required()
                                            ->maxLength(50)
                                            ->placeholder('e.g., ACCTNG 1, GE-1, ITW 101')
                                            ->helperText('Enter a unique code for the subject'),
                                        TextInput::make('title')
                                            ->label('Subject Title')
                                            ->required()
                                            ->maxLength(255)
                                            ->placeholder('e.g., Fundamentals of Accounting')
                                            ->helperText('Enter the full title of the subject'),
                                        TextInput::make('units')
                                            ->label('Units')
                                            ->numeric()
                                            ->default(3)
                                            ->required()
                                            ->minValue(1)
                                            ->maxValue(6),
                                        TextInput::make('lecture')
                                            ->label('Lecture Hours')
                                            ->numeric()
                                            ->default(3)
                                            ->minValue(0),
                                        TextInput::make('laboratory')
                                            ->label('Laboratory Hours')
                                            ->numeric()
                                            ->default(0)
                                            ->minValue(0),
                                        Select::make('academic_year')
                                            ->label('Academic Year')
                                            ->options([
                                                1 => '1st Year',
                                                2 => '2nd Year',
                                                3 => '3rd Year',
                                                4 => '4th Year',
                                            ])
                                            ->required()
                                            ->default(1),
                                        Select::make('semester')
                                            ->label('Semester')
                                            ->options([
                                                1 => '1st Semester',
                                                2 => '2nd Semester',
                                            ])
                                            ->required()
                                            ->default(1),
                                        Select::make('course_id')
                                            ->label('Course')
                                            ->options(fn () => Course::all()->pluck('code', 'id'))
                                            ->required()
                                            ->searchable()
                                            ->preload(),
                                        Select::make('classification')
                                            ->label('Classification')
                                            ->options([
                                                'credited' => 'Credited',
                                                'non_credited' => 'Non-Credited',
                                                'internal' => 'Internal',
                                            ])
                                            ->default('credited')
                                            ->required(),
                                    ])
                                    ->createOptionUsing(function (array $data) {
                                        $subject = Subject::create($data);

                                        return $subject->id;
                                    })
                                    ->afterStateUpdated(function (
                                        Forms\Set $set,
                                        $state
                                    ): void {
                                        $set('faculty_id', null);

                                        // Auto-populate subject_code based on selected subject
                                        if ($state) {
                                            $subject = Subject::find($state);
                                            if ($subject) {
                                                $set('subject_code', $subject->code);
                                                // Mark that this was set by dropdown, not manual entry
                                                $set('_subject_set_by_dropdown', true);
                                            }
                                        } else {
                                            $set('subject_code', null);
                                            $set('_subject_set_by_dropdown', false);
                                        }
                                    }),

                                // Manual Subject Code Input (fallback)
                                TextInput::make('subject_code')
                                    ->label('Subject Code (Manual Entry)')
                                    ->placeholder('e.g., ACCTNG 1, GE-1, ITW 101')
                                    ->visible(fn (Forms\Get $get): bool => $get('classification') === 'college')
                                    ->helperText('You can manually enter a subject code if the subject is not in the dropdown above.')
                                    ->live()
                                    ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get, $state): void {
                                        // Only clear subject_id if this is manual entry (not set by dropdown)
                                        if ($state && ! $get('_subject_set_by_dropdown')) {
                                            $set('subject_id', null);
                                        }
                                        // Reset the flag
                                        $set('_subject_set_by_dropdown', false);
                                    }),

                                // Hidden field to track dropdown state
                                Forms\Components\Hidden::make('_subject_set_by_dropdown')
                                    ->default(false),

                                // SHS Subject Selection
                                Select::make('subject_code')
                                    ->label('SHS Subject')
                                    ->placeholder('Select a subject...')
                                    ->options(function (Forms\Get $get) {
                                        $strandId = $get('shs_strand_id');
                                        if (! $strandId) {
                                            return [];
                                        }

                                        return StrandSubject::where('strand_id', $strandId)
                                            ->get()
                                            ->mapWithKeys(function ($subject) {
                                                $display = "{$subject->code} - {$subject->title}";
                                                if ($subject->grade_year) {
                                                    $display .= " ({$subject->grade_year})";
                                                }

                                                return [$subject->code => $display];
                                            });
                                    })
                                    ->searchable()
                                    ->required()
                                    ->preload()
                                    ->visible(fn (Forms\Get $get): bool => $get('classification') === 'shs')
                                    ->disabled(fn (Forms\Get $get): bool => ! $get('shs_strand_id'))
                                    ->helperText(function (Forms\Get $get): string {
                                        if (! $get('shs_strand_id')) {
                                            return 'Please select a track and strand first to see available subjects.';
                                        }

                                        return 'Choose the subject to be taught in this class.';
                                    })
                                    ->createOptionForm([
                                        TextInput::make('code')
                                            ->label('Subject Code')
                                            ->required()
                                            ->maxLength(50)
                                            ->placeholder('e.g., STEM-MATH11, ABM-ACCT11')
                                            ->helperText('Enter a unique code for the subject'),
                                        TextInput::make('title')
                                            ->label('Subject Title')
                                            ->required()
                                            ->maxLength(255)
                                            ->placeholder('e.g., General Mathematics, Fundamentals of Accountancy')
                                            ->helperText('Enter the full title of the subject'),
                                        Forms\Components\Textarea::make('description')
                                            ->label('Description')
                                            ->maxLength(500)
                                            ->placeholder('Brief description of the subject...')
                                            ->helperText('Optional description of the subject'),
                                        Select::make('grade_year')
                                            ->label('Grade Year')
                                            ->options([
                                                'Grade 11' => 'Grade 11',
                                                'Grade 12' => 'Grade 12',
                                            ])
                                            ->required()
                                            ->default('Grade 11'),
                                        Select::make('semester')
                                            ->label('Semester')
                                            ->options([
                                                '1st' => '1st Semester',
                                                '2nd' => '2nd Semester',
                                            ])
                                            ->required()
                                            ->default('1st'),
                                        Forms\Components\Hidden::make('strand_id')
                                            ->default(fn (Forms\Get $get): mixed => $get('../../shs_strand_id')),
                                    ])
                                    ->createOptionUsing(function (array $data, Forms\Get $get) {
                                        $data['strand_id'] = $get('shs_strand_id');
                                        $subject = StrandSubject::create($data);

                                        return $subject->code;
                                    })
                                    ->validationMessages([
                                        'required' => 'Please select a subject for this SHS class.',
                                    ]),

                                // SHS Track Selection
                                Select::make('shs_track_id')
                                    ->label('SHS Track')
                                    ->placeholder('Select a track...')
                                    ->options(fn () => ShsTrack::all()->pluck('track_name', 'id'))
                                    ->searchable()
                                    ->preload()
                                    ->live()
                                    ->visible(fn (Forms\Get $get): bool => $get('classification') === 'shs')
                                    ->required(fn (Forms\Get $get): bool => $get('classification') === 'shs')
                                    ->helperText('Choose the SHS track for this class (e.g., Academic Track, TVL Track)')
                                    ->createOptionForm([
                                        TextInput::make('track_name')
                                            ->label('Track Name')
                                            ->required()
                                            ->maxLength(255)
                                            ->placeholder('e.g., Academic Track, TVL Track')
                                            ->helperText('Enter the name of the new SHS track'),
                                        Forms\Components\Textarea::make('description')
                                            ->label('Description')
                                            ->maxLength(500)
                                            ->placeholder('Brief description of the track...')
                                            ->helperText('Optional description of the track'),
                                    ])
                                    ->createOptionUsing(function (array $data) {
                                        $track = ShsTrack::create($data);

                                        return $track->getKey();
                                    })
                                    ->afterStateUpdated(function (Forms\Set $set, $state): void {
                                        $set('shs_strand_id', null);
                                        $set('subject_code', null);
                                    })
                                    ->validationMessages([
                                        'required' => 'Please select an SHS track before proceeding.',
                                    ]),

                                // SHS Strand Selection
                                Select::make('shs_strand_id')
                                    ->label('SHS Strand')
                                    ->placeholder('Select a strand...')
                                    ->options(function (Forms\Get $get) {
                                        $trackId = $get('shs_track_id');
                                        if (! $trackId) {
                                            return [];
                                        }

                                        return ShsStrand::where('track_id', $trackId)
                                            ->pluck('strand_name', 'id');
                                    })
                                    ->searchable()
                                    ->preload()
                                    ->live()
                                    ->visible(fn (Forms\Get $get): bool => $get('classification') === 'shs')
                                    ->required(fn (Forms\Get $get): bool => $get('classification') === 'shs')
                                    ->disabled(fn (Forms\Get $get): bool => ! $get('shs_track_id'))
                                    ->helperText(function (Forms\Get $get): string {
                                        if (! $get('shs_track_id')) {
                                            return 'Please select a track first to see available strands.';
                                        }

                                        return 'Choose the specific strand within the selected track.';
                                    })
                                    ->createOptionForm([
                                        TextInput::make('strand_name')
                                            ->label('Strand Name')
                                            ->required()
                                            ->maxLength(255)
                                            ->placeholder('e.g., STEM, ABM, HUMSS, ICT')
                                            ->helperText('Enter the name of the new strand'),
                                        Forms\Components\Textarea::make('description')
                                            ->label('Description')
                                            ->maxLength(500)
                                            ->placeholder('Brief description of the strand...')
                                            ->helperText('Optional description of the strand'),
                                        Forms\Components\Hidden::make('track_id')
                                            ->default(fn (Forms\Get $get): mixed => $get('../../shs_track_id')),
                                    ])
                                    ->createOptionUsing(function (array $data, Forms\Get $get) {
                                        $data['track_id'] = $get('shs_track_id');
                                        $strand = ShsStrand::create($data);

                                        return $strand->getKey();
                                    })
                                    ->afterStateUpdated(function (Forms\Set $set, $state): void {
                                        $set('subject_code', null);
                                    })
                                    ->validationMessages([
                                        'required' => 'Please select an SHS strand before proceeding.',
                                    ]),

                                Select::make('faculty_id')
                                    ->label('Faculty')
                                    ->options(
                                        fn () => Faculty::all()->mapWithKeys(
                                            fn ($faculty) => [
                                                $faculty->id => $faculty->full_name,
                                            ]
                                        )
                                    )
                                    ->searchable()
                                    ->preload()
                                    ->placeholder('Select a faculty member...')
                                    ->helperText('Choose the faculty member who will teach this class'),

                                // College Academic Year
                                TextInput::make('academic_year')
                                    ->required()
                                    ->numeric()
                                    ->minValue(1)
                                    ->maxValue(4)
                                    ->label('Year Level')
                                    ->visible(fn (Forms\Get $get): bool => $get('classification') === 'college')
                                    ->default(fn (): int => 1),

                                // SHS Grade Level
                                Select::make('grade_level')
                                    ->label('Grade Level')
                                    ->placeholder('Select grade level...')
                                    ->options([
                                        'Grade 11' => 'Grade 11 (Junior High School)',
                                        'Grade 12' => 'Grade 12 (Senior High School)',
                                    ])
                                    ->required()
                                    ->visible(fn (Forms\Get $get): bool => $get('classification') === 'shs')
                                    ->default('Grade 11')
                                    ->helperText('Select the appropriate grade level for this SHS class')
                                    ->validationMessages([
                                        'required' => 'Please select a grade level for this SHS class.',
                                    ]),

                                Select::make('semester')
                                    ->options([
                                        '1' => '1st Semester',
                                        '2' => '2nd Semester',
                                        'summer' => 'Summer',
                                    ])
                                    ->required()
                                    ->default(function () {
                                        $settingsService = app(
                                            GeneralSettingsService::class
                                        );

                                        return $settingsService->getCurrentSemester();
                                    }),

                                TextInput::make('school_year')
                                    ->required()
                                    ->placeholder('e.g. 2023-2024')
                                    ->default(function () {
                                        $settingsService = app(
                                            GeneralSettingsService::class
                                        );

                                        return $settingsService->getCurrentSchoolYearString();
                                    }),

                                Select::make('section')
                                    ->label('Section')
                                    ->options([
                                        'A' => 'Section A',
                                        'B' => 'Section B',
                                        'C' => 'Section C',
                                        'D' => 'Section D',
                                    ])
                                    ->required()
                                    ->placeholder('Select section...')
                                    ->helperText('Choose the section for this class'),

                                Select::make('course_codes')
                                    ->label('Associated Courses')
                                    ->options(function () {
                                        $settingsService = app(
                                            GeneralSettingsService::class
                                        );
                                        $enrollmentCourses = $settingsService->getGlobalSettingsModel()
                                            ->enrollment_courses;
                                        if (empty($enrollmentCourses)) {
                                            return [];
                                        }

                                        return Course::whereIn(
                                            'id',
                                            $enrollmentCourses
                                        )
                                            ->get()
                                            ->mapWithKeys(function ($course) {
                                                $label = $course->title;
                                                if ($course->curriculum_year) {
                                                    $label .=
                                                        ' ('.
                                                        $course->curriculum_year.
                                                        ')';
                                                }

                                                return [$course->id => $label];
                                            });
                                    })
                                    ->multiple()
                                    ->searchable()
                                    ->preload()
                                    ->live()
                                    ->visible(fn (Forms\Get $get): bool => $get('classification') === 'college')
                                    ->afterStateUpdated(function (Forms\Set $set, $state): void {
                                        // Clear subject selection when courses change
                                        $set('subject_id', null);
                                        $set('subject_code', null);
                                        $set('faculty_id', null);
                                    })
                                    ->helperText(
                                        'Select which courses this class is available for. Subjects will be filtered based on your selection.'
                                    ),

                                Select::make('room_id')
                                    ->label('Room')
                                    ->options(
                                        fn () => Room::all()->pluck('name', 'id')
                                    )
                                    ->searchable()
                                    ->preload()
                                    ->createOptionForm([
                                        TextInput::make('name')
                                            ->label('Room Name')
                                            ->required()
                                            ->maxLength(255)
                                            ->placeholder('e.g., Computer Laboratory 1')
                                            ->live(onBlur: true)
                                            ->afterStateUpdated(function (Forms\Set $set, $state): void {
                                                if ($state) {
                                                    // Auto-generate slug from name
                                                    $slug = \Illuminate\Support\Str::slug($state, '-');
                                                    $slug = mb_strtoupper($slug);
                                                    $set('class_code', $slug);
                                                }
                                            })
                                            ->helperText('Enter the room name (slug will be auto-generated)'),
                                        TextInput::make('class_code')
                                            ->label('Room Code/Slug')
                                            ->required()
                                            ->maxLength(50)
                                            ->placeholder('AUTO-GENERATED')
                                            ->helperText('Auto-generated from room name, but you can modify it'),
                                    ])
                                    ->createOptionUsing(fn (array $data) => Room::create([
                                        'name' => $data['name'],
                                        'class_code' => $data['class_code'],
                                    ])->getKey()),

                                TextInput::make('maximum_slots')
                                    ->numeric()
                                    ->required()
                                    ->minValue(1)
                                    ->default(40)
                                    ->label('Maximum Slots'),
                            ]),
                        ]),

                        Section::make('Schedule')
                            ->description(
                                'Manage class schedule. Add multiple schedules if needed.'
                            )
                            ->schema([
                                Repeater::make('schedules')
                                    ->relationship()
                                    ->schema([
                                        Grid::make(4)->schema([
                                            Select::make('day_of_week')
                                                ->options([
                                                    'Monday' => 'Monday',
                                                    'Tuesday' => 'Tuesday',
                                                    'Wednesday' => 'Wednesday',
                                                    'Thursday' => 'Thursday',
                                                    'Friday' => 'Friday',
                                                    'Saturday' => 'Saturday',
                                                ])
                                                ->required()
                                                ->label('Day'),

                                            Forms\Components\TimePicker::make(
                                                'start_time'
                                            )
                                                ->required()
                                                ->seconds(false)
                                                ->label('Start Time'),

                                            Forms\Components\TimePicker::make(
                                                'end_time'
                                            )
                                                ->required()
                                                ->seconds(false)
                                                ->label('End Time')
                                                ->afterStateUpdated(function (
                                                    Forms\Set $set,
                                                    $state,
                                                    Forms\Get $get
                                                ): void {
                                                    $startTime = $get(
                                                        'start_time'
                                                    );
                                                    if ($startTime && $state && $state < $startTime) {
                                                        $set(
                                                            'end_time',
                                                            $startTime
                                                        );
                                                    }
                                                }),

                                            Select::make('room_id')
                                                ->label('Class Room')
                                                ->relationship(
                                                    name: 'Room',
                                                    titleAttribute: 'name'
                                                )
                                                ->searchable()
                                                ->createOptionForm([
                                                    TextInput::make('name')
                                                        ->label('Room Name')
                                                        ->required()
                                                        ->maxLength(255)
                                                        ->placeholder('e.g., Computer Laboratory 1')
                                                        ->live(onBlur: true)
                                                        ->afterStateUpdated(function (Forms\Set $set, $state): void {
                                                            if ($state) {
                                                                // Auto-generate slug from name
                                                                $slug = \Illuminate\Support\Str::slug($state, '-');
                                                                $slug = mb_strtoupper($slug);
                                                                $set('class_code', $slug);
                                                            }
                                                        })
                                                        ->helperText('Enter the room name (code will be auto-generated)'),
                                                    TextInput::make('class_code')
                                                        ->label('Room Code/Slug')
                                                        ->required()
                                                        ->maxLength(50)
                                                        ->placeholder('AUTO-GENERATED')
                                                        ->helperText('Auto-generated from room name, but you can modify it'),
                                                ])
                                                ->preload(),
                                        ]),
                                    ])
                                    ->defaultItems(1)
                                    ->reorderable(true)
                                    ->collapsible()
                                    ->rules([new ScheduleOverlapRule])
                                    ->validationMessages([
                                        'schedule_overlap' => 'The schedule overlaps with another.',
                                    ])
                                    ->cloneable()
                                    ->itemLabel(
                                        fn (array $state): ?string => $state[
                                            'day_of_week'
                                        ] ?? null
                                    ),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),

                Group::make()
                    ->schema([
                        Section::make('Timestamps')
                            ->schema([
                                Placeholder::make('created_at')
                                    ->label('Created at')
                                    ->content(
                                        fn (
                                            ?Classes $record
                                        ): string => $record?->created_at?->diffForHumans() ??
                                            'Never'
                                    ),

                                Placeholder::make('updated_at')
                                    ->label('Last modified at')
                                    ->content(
                                        fn (
                                            ?Classes $record
                                        ): string => $record?->updated_at?->diffForHumans() ??
                                            'Never'
                                    ),
                            ])
                            ->hidden(fn (?Classes $record): bool => ! $record instanceof Classes),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                BadgeColumn::make('classification')
                    ->label('Type')
                    ->colors([
                        'primary' => 'college',
                        'success' => 'shs',
                    ])
                    ->formatStateUsing(
                        fn (string $state): string => match ($state) {
                            'college' => 'College',
                            'shs' => 'SHS',
                            default => 'College',
                        }
                    )
                    ->sortable()
                    ->visible(fn (): bool => request()->query('activeTab', 'all') === 'all'),

                TextColumn::make('subject_code')
                    ->label('Subject')
                    ->searchable(
                        query: fn (Builder $query, string $search): Builder => $query
                            ->where('subject_code', 'like', "%{$search}%")
                            ->orWhereHas('subject', function ($q) use (
                                $search
                            ): void {
                                $q->where('title', 'like', "%{$search}%");
                            })
                            ->orWhereHas('ShsSubject', function ($q) use (
                                $search
                            ): void {
                                $q->where('title', 'like', "%{$search}%");
                            }),
                        isIndividual: true
                    )
                    ->sortable()
                    ->formatStateUsing(fn (Classes $record): string =>
                        // Display the subject code, or fallback if empty
                        $record->subject_code ?: 'No Subject Code')
                    ->description(function (Classes $record): string {
                        if ($record->isShs()) {
                            return $record->ShsSubject?->title.
                                ' • Track/Strand: '.
                                $record->formatted_track_strand;
                        }

                        // Try to get subject title from relationship first
                        $subjectTitle = $record->Subject?->title;

                        // If no relationship, try to find subject by code
                        if (! $subjectTitle && $record->subject_code) {
                            $subject = Subject::where('code', $record->subject_code)->first();
                            $subjectTitle = $subject?->title;
                        }

                        $subjectTitle = $subjectTitle ?: 'Unknown Subject';

                        return $subjectTitle.
                            ' • Courses: '.
                            $record->formatted_course_codes;
                    }),

                // College-specific columns
                TextColumn::make('formatted_course_codes')
                    ->label('Courses')
                    ->visible(fn (): bool => in_array(request()->query('activeTab', 'all'), ['all', 'college']))
                    ->toggleable(isToggledHiddenByDefault: request()->query('activeTab', 'all') === 'all'),

                // SHS-specific columns
                TextColumn::make('ShsTrack.track_name')
                    ->label('Track')
                    ->visible(fn (): bool => in_array(request()->query('activeTab', 'all'), ['all', 'shs']))
                    ->toggleable(isToggledHiddenByDefault: request()->query('activeTab', 'all') === 'all'),

                TextColumn::make('ShsStrand.strand_name')
                    ->label('Strand')
                    ->visible(fn (): bool => in_array(request()->query('activeTab', 'all'), ['all', 'shs']))
                    ->toggleable(isToggledHiddenByDefault: request()->query('activeTab', 'all') === 'all'),

                TextColumn::make('faculty_name')
                    ->label('Faculty')
                    ->getStateUsing(fn (Classes $record): string => $record->Faculty?->full_name ?? 'No faculty assigned')
                    ->sortable(query: fn (Builder $query, string $direction): Builder => $query->leftJoin('faculty', 'classes.faculty_id', '=', 'faculty.id')
                        ->orderByRaw("CONCAT(faculty.last_name, ', ', faculty.first_name) {$direction}"))
                    ->toggleable()
                    ->placeholder('No faculty assigned')
                    ->description(fn (Classes $record): ?string => $record->Faculty?->department ? "Dept: {$record->Faculty->department}" : null
                    ),

                TextColumn::make('section')
                    ->label('Section')
                    ->searchable()
                    ->sortable(),

                BadgeColumn::make('year_level')
                    ->label('Year/Grade Level')
                    ->color(function (Classes $record): string {
                        if ($record->isShs()) {
                            return match ($record->grade_level) {
                                'Grade 11' => 'info',
                                'Grade 12' => 'success',
                                default => 'gray',
                            };
                        }

                        return match ($record->academic_year) {
                            '1' => 'info',
                            '2' => 'success',
                            '3' => 'warning',
                            '4' => 'danger',
                            default => 'gray',
                        };
                    })
                    ->formatStateUsing(function (Classes $record): string {
                        if ($record->isShs()) {
                            return $record->grade_level ?? 'N/A';
                        }

                        return match ($record->academic_year) {
                            '1' => '1st Year',
                            '2' => '2nd Year',
                            '3' => '3rd Year',
                            '4' => '4th Year',
                            default => $record->academic_year ?? 'N/A',
                        };
                    })
                    ->sortable(),

                BadgeColumn::make('semester')
                    ->colors([
                        'primary' => '1st',
                        'secondary' => '2nd',
                        'warning' => 'summer',
                    ])
                    ->formatStateUsing(
                        fn (string $state): string => match ($state) {
                            '1st' => '1st Sem',
                            '2nd' => '2nd Sem',
                            'summer' => 'Summer',
                            default => $state,
                        }
                    )
                    ->searchable()
                    ->sortable(),

                TextColumn::make('school_year')->searchable()->sortable(),

                TextColumn::make('student_count')
                    ->label('Enrolled')
                    ->counts('class_enrollments')
                    ->sortable()
                    ->formatStateUsing(
                        fn (
                            Model $record
                        ): string => "{$record->class_enrollments_count} / {$record->maximum_slots}"
                    ),

                TextColumn::make('maximum_slots')
                    ->label('Max Slots')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('classification')
                    ->label('Class Type')
                    ->options([
                        'college' => 'College',
                        'shs' => 'Senior High School',
                    ])
                    ->placeholder('All Types'),

                SelectFilter::make('course')
                    ->label('Course (College)')
                    ->options(
                        fn () => Course::all()
                            ->pluck('code', 'id')
                            ->sortByDesc('code')
                    )
                    ->searchable()
                    ->preload()
                    ->placeholder('Filter by course')
                    ->query(function (Builder $query, array $data): void {
                        if (! empty($data['value'])) {
                            $query->where('classification', 'college')
                                ->whereHas('subject', function ($q) use (
                                    $data
                                ): void {
                                    $q->where('course_id', $data['value']);
                                });
                        }
                    }),

                SelectFilter::make('shs_track_id')
                    ->label('SHS Track')
                    ->options(fn () => ShsTrack::all()->pluck('track_name', 'id'))
                    ->searchable()
                    ->preload()
                    ->placeholder('Filter by track')
                    ->query(function (Builder $query, array $data): void {
                        if (! empty($data['value'])) {
                            $query->where('classification', 'shs')
                                ->where('shs_track_id', $data['value']);
                        }
                    }),

                SelectFilter::make('shs_strand_id')
                    ->label('SHS Strand')
                    ->options(fn () => ShsStrand::all()->pluck('strand_name', 'id'))
                    ->searchable()
                    ->preload()
                    ->placeholder('Filter by strand')
                    ->query(function (Builder $query, array $data): void {
                        if (! empty($data['value'])) {
                            $query->where('classification', 'shs')
                                ->where('shs_strand_id', $data['value']);
                        }
                    }),

                SelectFilter::make('subject_code')
                    ->label('Subject')
                    ->options(
                        fn (): array => Subject::query()
                            ->when(
                                request()->input('filters.course'),
                                fn ($query, $courseId) => $query->where(
                                    'course_id',
                                    $courseId
                                )
                            )
                            ->pluck('title', 'code')
                            ->toArray()
                    )
                    ->searchable()
                    ->preload(),

                SelectFilter::make('room')
                    ->label('Room')
                    ->options(fn () => Room::all()->pluck('name', 'id'))
                    ->searchable()
                    ->preload()

                    ->query(function (Builder $query, array $data): void {
                        if (! empty($data['value'])) {
                            $query->where(function ($q) use ($data): void {
                                $q->where(
                                    'room_id',
                                    $data['value']
                                )->orWhereHas('schedules', function ($q) use (
                                    $data
                                ): void {
                                    $q->where('room_id', $data['value']);
                                });
                            });
                        }
                    }),

                SelectFilter::make('faculty_id')
                    ->label('Faculty')
                    ->options(
                        fn () => Faculty::all()->mapWithKeys(
                            fn ($faculty) => [
                                $faculty->id => $faculty->full_name,
                            ]
                        )
                    )
                    ->searchable()
                    ->preload(),

                SelectFilter::make('academic_year')
                    ->options([
                        '1' => '1st Year (College)',
                        '2' => '2nd Year (College)',
                        '3' => '3rd Year (College)',
                        '4' => '4th Year (College)',
                    ])
                    ->label('College Year Level')
                    ->indicator('College Year')
                    ->query(function (Builder $query, array $data): void {
                        if (! empty($data['value'])) {
                            $query->where('classification', 'college')
                                ->where('academic_year', $data['value']);
                        }
                    }),

                SelectFilter::make('grade_level')
                    ->options([
                        'Grade 11' => 'Grade 11',
                        'Grade 12' => 'Grade 12',
                    ])
                    ->label('SHS Grade Level')
                    ->indicator('SHS Grade')
                    ->query(function (Builder $query, array $data): void {
                        if (! empty($data['value'])) {
                            $query->where('classification', 'shs')
                                ->where('grade_level', $data['value']);
                        }
                    }),

                SelectFilter::make('semester')
                    ->options([
                        '1st' => '1st Semester',
                        '2nd' => '2nd Semester',
                        'summer' => 'Summer',
                    ])
                    ->label('Semester')
                    ->indicator('Semester'),

                Filter::make('available_slots')
                    ->label('Has Available Slots')
                    ->indicator('Available Slots')
                    ->query(
                        fn (Builder $query): Builder => $query->whereColumn(
                            'maximum_slots',
                            '>',
                            'class_enrollments_count'
                        )
                    ),

                TernaryFilter::make('fully_enrolled')
                    ->label('Fully Enrolled')
                    ->indicator('Fully Enrolled')
                    ->queries(
                        true: fn (Builder $query) => $query->whereColumn(
                            'maximum_slots',
                            '<=',
                            'class_enrollments_count'
                        ),
                        false: fn (Builder $query) => $query->whereColumn(
                            'maximum_slots',
                            '>',
                            'class_enrollments_count'
                        )
                    ),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->tooltip('View class details')
                    ->icon('heroicon-o-eye'),
                Tables\Actions\EditAction::make()
                    ->tooltip('Edit class')
                    ->icon('heroicon-o-pencil'),
                Tables\Actions\DeleteAction::make()
                    ->tooltip('Delete class')
                    ->icon('heroicon-o-trash'),
                Action::make('copyClass')
                    ->label('Copy Class')
                    ->icon('heroicon-o-document-duplicate')
                    ->form([
                        Select::make('section')
                            ->label('New Section')
                            ->options([
                                'A' => 'Section A',
                                'B' => 'Section B',
                                'C' => 'Section C',
                                'D' => 'Section D',
                            ])
                            ->required()
                            ->placeholder('Select section for the copy...'),
                    ])
                    ->action(function (array $data, Classes $record): void {
                        // Duplicate the class except for schedules and section
                        $newClass = $record->replicate(['section']);
                        $newClass->section = $data['section'];
                        // Unset any *_count attributes that are not real columns
                        foreach ($newClass->getAttributes() as $key => $value) {
                            if (str_ends_with($key, '_count')) {
                                unset($newClass->{$key});
                            }
                        }
                        $newClass->push();
                        // Do not copy schedules
                    })
                    ->color('primary')
                    ->modalHeading('Copy Class')
                    ->modalDescription(
                        'This will create a new class with the same data except for schedules. Assign a new section.'
                    ),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [RelationManagers\ClassEnrollmentsRelationManager::class];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListClasses::route('/'),
            'create' => Pages\CreateClass::route('/create'),
            'edit' => Pages\EditClass::route('/{record}/edit'),
            'view' => Pages\ViewClass::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withCount('class_enrollments')
            ->currentAcademicPeriod();
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist->schema([
            InfolistSection::make('Class Information')
                ->schema([
                    TextEntry::make('subject.title')
                        ->label('Subject')
                        ->columnSpanFull(),
                    TextEntry::make('formatted_course_codes')
                        ->label('Associated Courses')
                        ->badge()
                        ->separator(', ')
                        ->columnSpanFull(),
                    TextEntry::make('section')->label('Section'),
                    TextEntry::make('academic_year')
                        ->label('Year Level')
                        ->formatStateUsing(
                            fn (string $state): string => match ($state) {
                                '1' => '1st Year',
                                '2' => '2nd Year',
                                '3' => '3rd Year',
                                '4' => '4th Year',
                                default => $state,
                            }
                        ),
                    TextEntry::make('semester')
                        ->label('Semester')
                        ->formatStateUsing(
                            fn (string $state): string => match ($state) {
                                '1st' => '1st Semester',
                                '2nd' => '2nd Semester',
                                'summer' => 'Summer',
                                default => $state,
                            }
                        ),
                    TextEntry::make('school_year')->label('School Year'),
                    TextEntry::make('student_count')
                        ->label('Enrolled Students')
                        ->formatStateUsing(
                            fn (
                                Model $record
                            ): string => "{$record->class_enrollments_count} / {$record->maximum_slots}"
                        )
                        ->color(
                            fn (
                                string $state,
                                Model $record
                            ): string => $record->class_enrollments_count <
                            $record->maximum_slots
                                ? 'success'
                                : 'danger'
                        )
                        ->icon(
                            fn (
                                string $state,
                                Model $record
                            ): string => $record->class_enrollments_count <
                            $record->maximum_slots
                                ? 'heroicon-o-check-circle'
                                : 'heroicon-o-exclamation-triangle'
                        ),
                ])
                ->columns(2),

            InfolistSection::make('Faculty Information')
                ->schema([
                    ImageEntry::make('Faculty.avatar_url')
                        ->label('')
                        ->circular()
                        ->grow(false)
                        ->width(50)
                        ->height(50),
                    TextEntry::make('faculty.full_name')
                        ->label('Faculty')
                        ->columnSpanFull(),
                ])
                ->columns(2),

            InfolistSection::make('Class Schedule')
                ->collapsed()
                ->schema([
                    ViewEntry::make('')
                        ->view('infolists.components.timetable')
                        ->columnSpanFull(),
                ]),
        ]);
    }

    private function shouldPersistTableSearchInSession(): bool
    {
        return true;
    }

    private function shouldPersistTableColumnSearchInSession(): bool
    {
        return true;
    }
}
